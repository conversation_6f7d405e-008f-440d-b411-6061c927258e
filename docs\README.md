# PCO Portfolio Website

A personal portfolio website created for the Program & Career Orientation (PCO) course at HZ University of Applied Sciences.

## 📋 Project Overview

This website showcases my journey into the HBO-ICT program and serves as a comprehensive portfolio demonstrating my understanding of web development fundamentals, personal reflection, and academic progress.

## 🎯 Assignment Requirements Met

### Content Requirements

- ✅ **Home Page**: Landing page with motivation, 2 images, 2 links, 2 paragraphs, and 1 list
- ✅ **Profile Page**: Personal information, characteristics, hobbies, and social media links
- ✅ **Dashboard**: Study monitor with HTML table showing courses, grades, and NBSA progress
- ✅ **Blog**: Multiple blog posts covering study choice, SWOT analysis, and personal experiences
- ✅ **FAQ**: Comprehensive answers to HZ University facility and procedure questions

### Technical Requirements

- ✅ **Bootstrap Framework**: Primary styling framework for responsive design
- ✅ **Custom CSS**: Additional styling for unique design elements
- ✅ **Semantic HTML5**: Proper use of semantic tags (nav, main, aside, article, section, footer)
- ✅ **SEO Optimization**: Title tags, meta descriptions, heading hierarchy, alt text
- ✅ **Navigation System**: Main navigation and aside menu with useful links
- ✅ **Responsive Design**: Mobile-friendly layout using Bootstrap grid system

### Design Features

- ✅ **Consistent Branding**: HZ University color scheme and professional appearance
- ✅ **User Experience**: Intuitive navigation and clear content hierarchy
- ✅ **Accessibility**: Proper contrast, focus indicators, and screen reader support
- ✅ **Performance**: Optimized images and efficient CSS

## 🗂️ File Structure

```
PCO/
├── index.html                    # Home page
├── profile.html                  # Personal profile page
├── dashboard.html                # Study progress dashboard
├── blog.html                     # Blog feed page
├── faq.html                      # FAQ page
├── blog-post-study-choice.html   # Individual blog post
├── blog-post-swot-analysis.html  # Individual blog post
├── css/
│   └── style.css                 # Custom CSS styles
├── images/
│   └── placeholder-info.txt      # Image requirements documentation
├── js/                           # JavaScript files (if needed)
└── README.md                     # This file
```

## 🎨 Design System

### Color Palette

- **Primary**: #0066cc (HZ Blue)
- **Secondary**: #004499 (Dark Blue)
- **Accent**: #ff6600 (Orange)
- **Success**: #28a745 (Green)
- **Warning**: #ffc107 (Yellow)

### Typography

- **Font Family**: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif
- **Heading Hierarchy**: Proper H1-H6 structure for SEO
- **Line Height**: 1.6 for optimal readability

### Components

- **Navigation**: Sticky top navigation with active page highlighting
- **Cards**: Consistent card design with hover effects
- **Buttons**: Custom styled buttons with hover animations
- **Tables**: Professional table styling for dashboard data

## 🚀 Features

### Navigation

- Responsive navigation bar with mobile hamburger menu
- Active page highlighting
- Consistent aside menu with useful HZ University links

### Dashboard

- Comprehensive study monitor table
- Progress visualization with progress bars
- NBSA boundary tracking
- Color-coded status indicators

### Blog System

- Blog feed with post previews
- Individual blog post pages
- Category system with badges
- Related posts and social sharing

### FAQ System

- Accordion-style FAQ layout
- Comprehensive answers to HZ University questions
- Search-friendly content structure

## 📱 Responsive Design

The website is fully responsive and tested on:

- Desktop (1200px+)
- Tablet (768px - 1199px)
- Mobile (320px - 767px)

## ♿ Accessibility Features

- Semantic HTML5 structure
- Proper heading hierarchy
- Alt text for all images
- Keyboard navigation support
- High contrast mode support
- Screen reader compatibility

## 🔧 Technologies Used

- **HTML5**: Semantic markup and structure
- **CSS3**: Custom styling and animations
- **Bootstrap 5.3.2**: Responsive framework and components
- **Bootstrap Icons**: Icon library for UI elements

## 📈 SEO Optimization

- Unique title tags for each page
- Descriptive meta descriptions
- Proper heading hierarchy (H1-H6)
- Alt text for all images
- Semantic HTML5 structure
- Clean URL structure

## 🎓 Learning Outcomes

This project demonstrates:

- Understanding of HTML5 semantic structure
- Proficiency with CSS and Bootstrap framework
- Responsive web design principles
- SEO best practices
- User experience design
- Personal reflection and self-analysis skills

## 🔄 Future Enhancements

Potential improvements for future iterations:

- Add JavaScript interactivity
- Implement a contact form
- Add more blog posts
- Include project portfolio section
- Add dark mode toggle
- Implement search functionality

## 📝 Assignment Context

**Course**: Program & Career Orientation (PCO)  
**Institution**: HZ University of Applied Sciences  
**Program**: HBO-ICT  
**Academic Year**: 2024-2025

## 📞 Contact

For questions about this project or my studies:

- **Email**: [Your HZ Email]
- **LinkedIn**: [Your LinkedIn Profile]
- **GitHub**: [Your GitHub Profile]

---

_This website was created as part of the PCO course requirements and represents my commitment to learning and professional development in the ICT field._
