<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      High School Education: Building Technical Foundations | My ICT Portfolio
    </title>
    <meta
      name="description"
      content="My journey through B<PERSON><PERSON>thy <PERSON> in Budapest (2018-2022), where I built a solid foundation in programming, web development, and computer science fundamentals."
    />

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Bootstrap Icons -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css"
    />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css" />
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
      <div class="container">
        <a class="navbar-brand" href="../index.html">
          <img
            src="../assets/images/hz-logo.jpg"
            alt="HZ University of Applied Sciences Logo"
            height="30"
            class="me-2"
          />
          My ICT Portfolio
        </a>
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link" href="../index.html">Home</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="../pages/profile.html">Profile</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="../pages/dashboard.html">Dashboard</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="../pages/faq.html">FAQ</a>
            </li>
            <li class="nav-item">
              <a class="nav-link active" href="../pages/blog.html">Blog</a>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="container my-5">
      <!-- Breadcrumb Navigation -->
      <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a href="../index.html">Home</a></li>
          <li class="breadcrumb-item"><a href="../pages/blog.html">Blog</a></li>
          <li class="breadcrumb-item active" aria-current="page">
            High School Education
          </li>
        </ol>
      </nav>

      <!-- Blog Post Header -->
      <article class="row">
        <div class="col-lg-8 mx-auto">
          <header class="mb-5">
            <div class="d-flex align-items-center mb-3">
              <span class="badge bg-success me-3 fs-6 py-2 px-3"
                >Programming Journey</span
              >
              <small class="text-muted">
                <i class="bi bi-calendar3 me-1"></i>2018-2022 (High School)
                <span class="mx-2">•</span>
                <i class="bi bi-clock me-1"></i>8 min read
                <span class="mx-2">•</span>
                <i class="bi bi-tag me-1"></i>Part 2 of 5
              </small>
            </div>
            <h1 class="display-5 fw-bold text-primary mb-4">
              High School Education: Building Technical Foundations at Bláthy
              Ottó School
            </h1>
            <p class="lead text-muted">
              My strategic choice to attend Bláthy Ottó School in Budapest
              (2018-2022) for specialized IT education, covering my progression
              from Python basics to advanced web development frameworks and
              successful completion of the Hungarian "érettségi" graduation
              exam.
            </p>
          </header>

          <!-- Blog Post Content -->
          <div class="blog-content">
            <!-- Introduction -->
            <section class="mb-5">
              <div class="alert alert-success border-0 mb-4">
                <i class="bi bi-mortarboard me-2"></i>
                <strong>High School Years:</strong> This post covers my formal
                IT education from 2018-2022 at Bláthy Ottó School in Budapest,
                where I built a comprehensive foundation in programming, web
                development, and computer science theory.
              </div>

              <p class="fs-5">
                After my positive early experiences with Scratch and robotics, I
                knew I wanted to pursue formal IT education. The decision to
                attend <strong>Bláthy Ottó School in Budapest</strong>
                was strategic and well-researched, setting the stage for four
                transformative years of intensive technical learning.
              </p>
            </section>

            <!-- School Selection -->
            <section class="mb-5">
              <h2 class="h3 text-primary mb-4">
                <i class="bi bi-search me-2"></i>Strategic School Selection
              </h2>

              <div class="row">
                <div class="col-md-8">
                  <p>
                    For my high school education, I made a deliberate choice to
                    attend
                    <strong>Bláthy Ottó School in Budapest</strong>,
                    specifically selecting this institution for its excellent IT
                    education programs. This decision required careful research
                    and consideration of various schools in the Budapest area.
                  </p>

                  <p>
                    At Bláthy Ottó School, I pursued dual tracks in
                    <strong>Network Building and Software Development</strong>,
                    which provided me with a comprehensive foundation in both
                    hardware and software aspects of IT. This dual approach
                    would prove invaluable in understanding the complete
                    technology stack.
                  </p>

                  <div class="bg-light p-4 rounded mb-4">
                    <h4 class="h5 text-primary mb-3">
                      Why Bláthy Ottó School?
                    </h4>
                    <ul class="mb-0">
                      <li>
                        <strong>Specialized IT Programs:</strong> Dedicated
                        tracks for software development and networking
                      </li>
                      <li>
                        <strong>Modern Curriculum:</strong> Up-to-date
                        technologies and industry-relevant skills
                      </li>
                      <li>
                        <strong>Experienced Faculty:</strong> Teachers with
                        real-world IT industry experience
                      </li>
                      <li>
                        <strong>Practical Focus:</strong> Hands-on learning with
                        real projects and applications
                      </li>
                      <li>
                        <strong>Budapest Location:</strong> Access to Hungary's
                        tech hub and opportunities
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="card bg-success text-white">
                    <div class="card-body text-center">
                      <i class="bi bi-building display-4 mb-3"></i>
                      <h5>Bláthy Ottó School</h5>
                      <p class="small mb-2">Budapest, Hungary</p>
                      <p class="small mb-0">
                        IT Specialized Education<br />2018-2022
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            <!-- Early High School Years -->
            <section class="mb-5">
              <h2 class="h3 text-primary mb-4">
                <i class="bi bi-play-circle me-2"></i>Early High School Years
                (2018-2020)
              </h2>

              <p>
                During my first two years, I built a solid foundation with core
                programming concepts and computer science theory. This period
                was crucial for establishing the fundamental knowledge that
                would support more advanced learning later.
              </p>

              <div class="row">
                <div class="col-md-6">
                  <div class="card border-primary mb-3">
                    <div class="card-header bg-primary text-white">
                      <h5 class="mb-0">
                        <i class="bi bi-code-square me-2"></i>Programming
                        Foundation
                      </h5>
                    </div>
                    <div class="card-body">
                      <ul class="mb-0">
                        <li>
                          <strong>Python programming</strong> - My first real
                          programming language beyond Scratch
                        </li>
                        <li>
                          <strong>HTML and CSS fundamentals</strong> -
                          Introduction to web development
                        </li>
                        <li>
                          <strong>Basic JavaScript</strong> - Client-side
                          scripting introduction
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="card border-info mb-3">
                    <div class="card-header bg-info text-white">
                      <h5 class="mb-0">
                        <i class="bi bi-cpu me-2"></i>Computer Science Theory
                      </h5>
                    </div>
                    <div class="card-body">
                      <ul class="mb-0">
                        <li>
                          <strong>Extensive algorithmics courses</strong> -
                          Developing computational thinking
                        </li>
                        <li>
                          <strong>Number system conversions</strong> - Essential
                          for networking knowledge
                        </li>
                        <li>
                          <strong>Computer architecture theory</strong> -
                          Understanding hardware fundamentals
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div class="bg-primary text-white p-4 rounded mb-4">
                <h4 class="mb-3">
                  <i class="bi bi-star me-2"></i>Hungarian Computer Science
                  Heritage
                </h4>
                <p class="mb-0">
                  During our computer architecture studies, we learned
                  extensively about
                  <strong>John von Neumann</strong>, the Hungarian-American
                  computer science pioneer who developed the fundamental
                  architecture that most modern computers still use today. This
                  connection to Hungarian innovation in technology was
                  particularly inspiring and showed me the significant
                  contributions Hungary has made to the field of computer
                  science.
                </p>
              </div>
            </section>

            <!-- Advanced High School Years -->
            <section class="mb-5">
              <h2 class="h3 text-primary mb-4">
                <i class="bi bi-rocket me-2"></i>Advanced High School Years
                (2020-2022)
              </h2>

              <p>
                As I progressed through my later high school years, the
                curriculum became more sophisticated, introducing modern
                frameworks and advanced programming concepts that prepared me
                for real-world development work.
              </p>

              <div class="row">
                <div class="col-md-8">
                  <h5 class="text-primary mb-3">
                    Advanced Technologies Mastered
                  </h5>
                  <div class="row">
                    <div class="col-md-6">
                      <ul>
                        <li>
                          <strong>Bootstrap framework</strong> - Advanced
                          responsive web design
                        </li>
                        <li>
                          <strong>C# programming</strong> - Object-oriented
                          programming concepts
                        </li>
                        <li>
                          <strong>JavaScript ES6+</strong> - Modern JavaScript
                          features and frameworks
                        </li>
                      </ul>
                    </div>
                    <div class="col-md-6">
                      <ul>
                        <li>
                          <strong>Database fundamentals</strong> - SQL and
                          database design principles
                        </li>
                        <li>
                          <strong>Version control</strong> - Git and
                          collaborative development
                        </li>
                        <li>
                          <strong>Software engineering</strong> - Project
                          management and methodologies
                        </li>
                      </ul>
                    </div>
                  </div>

                  <div class="bg-success text-white p-3 rounded mb-4">
                    <h5 class="mb-2">
                      <i class="bi bi-trophy me-2"></i>Graduation Achievement
                    </h5>
                    <p class="mb-0">
                      I successfully completed my
                      <strong>"érettségi"</strong> (Hungarian high school
                      graduation exam) at the end of 12th grade, achieving
                      excellent results that opened doors for advanced
                      opportunities and demonstrated my mastery of both
                      technical and academic subjects.
                    </p>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="card bg-info text-white">
                    <div class="card-body">
                      <h6>Technologies Mastered</h6>
                      <div class="d-flex flex-wrap gap-1">
                        <span class="badge bg-light text-dark">Python</span>
                        <span class="badge bg-light text-dark">HTML/CSS</span>
                        <span class="badge bg-light text-dark">Bootstrap</span>
                        <span class="badge bg-light text-dark">C#</span>
                        <span class="badge bg-light text-dark">JavaScript</span>
                        <span class="badge bg-light text-dark">SQL</span>
                        <span class="badge bg-light text-dark">Git</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            <!-- Key Learning Outcomes -->
            <section class="mb-5">
              <h2 class="h3 text-primary mb-4">
                <i class="bi bi-lightbulb me-2"></i>Key Learning Outcomes
              </h2>

              <div class="row">
                <div class="col-md-4">
                  <div class="card h-100 border-primary">
                    <div class="card-body text-center">
                      <i class="bi bi-layers text-primary display-4 mb-3"></i>
                      <h5 class="card-title">Full-Stack Understanding</h5>
                      <p class="card-text">
                        Gained comprehensive knowledge of both front-end and
                        back-end development, understanding how different
                        technologies work together.
                      </p>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="card h-100 border-success">
                    <div class="card-body text-center">
                      <i class="bi bi-people text-success display-4 mb-3"></i>
                      <h5 class="card-title">Collaborative Development</h5>
                      <p class="card-text">
                        Learned to work effectively in teams, using version
                        control and following software engineering best
                        practices.
                      </p>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="card h-100 border-info">
                    <div class="card-body text-center">
                      <i class="bi bi-graph-up text-info display-4 mb-3"></i>
                      <h5 class="card-title">Progressive Skill Building</h5>
                      <p class="card-text">
                        Experienced systematic skill development from basic
                        concepts to advanced frameworks and professional
                        development practices.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            <!-- Academic Excellence -->
            <section class="mb-5">
              <h2 class="h3 text-primary mb-4">
                <i class="bi bi-award me-2"></i>Academic Excellence and
                Recognition
              </h2>

              <div class="bg-light p-4 rounded">
                <div class="row">
                  <div class="col-md-8">
                    <h5 class="text-primary mb-3">Outstanding Performance</h5>
                    <p>
                      My excellent academic performance throughout high school
                      was recognized through consistently high grades and
                      positive feedback from instructors. This strong foundation
                      in both theoretical knowledge and practical skills
                      positioned me well for advanced opportunities.
                    </p>
                    <p class="mb-0">
                      The combination of rigorous coursework, hands-on projects,
                      and comprehensive understanding of IT fundamentals
                      prepared me not just for graduation, but for the next
                      phase of my educational journey.
                    </p>
                  </div>
                  <div class="col-md-4 text-center">
                    <i
                      class="bi bi-mortarboard-fill text-success display-1 mb-3"
                    ></i>
                    <h6 class="text-success">Érettségi Completed</h6>
                    <p class="small text-muted mb-0">
                      Hungarian High School<br />Graduation Exam<br />Excellent
                      Results
                    </p>
                  </div>
                </div>
              </div>
            </section>

            <!-- Conclusion -->
            <section class="mb-5">
              <div class="bg-primary text-white p-4 rounded">
                <h3 class="mb-3">
                  <i class="bi bi-arrow-right-circle me-2"></i>Foundation for
                  Advanced Learning
                </h3>
                <p class="mb-3">
                  My four years at Bláthy Ottó School provided exactly what I
                  had hoped for: a comprehensive, modern IT education that
                  bridged theoretical knowledge with practical application. The
                  progression from basic Python programming to advanced web
                  development frameworks gave me a solid foundation in multiple
                  programming paradigms and technologies.
                </p>
                <p class="mb-3">
                  The dual focus on Network Building and Software Development
                  proved particularly valuable, as it gave me a holistic
                  understanding of how software and hardware systems interact.
                  This knowledge would become increasingly important as I moved
                  toward more complex development projects.
                </p>
                <p class="mb-0">
                  Most importantly, the excellent academic results and
                  comprehensive skill set I developed during these years opened
                  the door to advanced opportunities, including the
                  scholarship-based internship that would define my 13th grade
                  experience and further accelerate my technical growth.
                </p>
              </div>
            </section>

            <!-- Navigation -->
            <section class="mb-4">
              <div class="d-flex justify-content-between align-items-center">
                <a
                  href="blog-post-early-programming.html"
                  class="btn btn-outline-primary"
                >
                  <i class="bi bi-arrow-left me-2"></i>Previous: Early
                  Programming
                </a>
                <div class="text-center">
                  <small class="text-muted d-block">Part 2 of 5</small>
                  <small class="text-muted">2018-2022 • High School</small>
                </div>
                <a
                  href="blog-post-advanced-training.html"
                  class="btn btn-primary"
                >
                  Next: Advanced Training <i class="bi bi-arrow-right ms-2"></i>
                </a>
              </div>
            </section>
          </div>
        </div>
      </article>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
      <div class="container">
        <div class="row">
          <div class="col-md-6">
            <p>&copy; 2024 My ICT Portfolio. All rights reserved.</p>
          </div>
          <div class="col-md-6 text-md-end">
            <p>HZ University of Applied Sciences - ICT Program</p>
          </div>
        </div>
      </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  </body>
</html>
