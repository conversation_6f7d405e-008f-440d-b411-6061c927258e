<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>
      Early Programming Foundation: My First Steps into Coding | My ICT
      Portfolio
    </title>
    <meta
      name="description"
      content="Discover how my programming journey began in elementary school with Scratch programming, robotics workshops, and the inspiring influence of my IT professional grandparents."
    />

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Bootstrap Icons -->
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css"
    />
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css" />
  </head>
  <body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
      <div class="container">
        <a class="navbar-brand" href="../index.html">
          <img
            src="../assets/images/hz-logo.jpg"
            alt="HZ University of Applied Sciences Logo"
            height="30"
            class="me-2"
          />
          My ICT Portfolio
        </a>
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav ms-auto">
            <li class="nav-item">
              <a class="nav-link" href="../index.html">Home</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="../pages/profile.html">Profile</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="../pages/dashboard.html">Dashboard</a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="../pages/faq.html">FAQ</a>
            </li>
            <li class="nav-item">
              <a class="nav-link active" href="../pages/blog.html">Blog</a>
            </li>
          </ul>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="container my-5">
      <!-- Breadcrumb Navigation -->
      <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
          <li class="breadcrumb-item"><a href="../index.html">Home</a></li>
          <li class="breadcrumb-item"><a href="../pages/blog.html">Blog</a></li>
          <li class="breadcrumb-item active" aria-current="page">
            Early Programming Foundation
          </li>
        </ol>
      </nav>

      <!-- Blog Post Header -->
      <article class="row">
        <div class="col-lg-8 mx-auto">
          <header class="mb-5">
            <div class="d-flex align-items-center mb-3">
              <span class="badge bg-primary me-3 fs-6 py-2 px-3"
                >Programming Journey</span
              >
              <small class="text-muted">
                <i class="bi bi-calendar3 me-1"></i>2011-2013 (Ages 11-13)
                <span class="mx-2">•</span>
                <i class="bi bi-clock me-1"></i>5 min read
                <span class="mx-2">•</span>
                <i class="bi bi-tag me-1"></i>Part 1 of 5
              </small>
            </div>
            <h1 class="display-5 fw-bold text-primary mb-4">
              Early Programming Foundation: My First Steps into Coding
            </h1>
            <p class="lead text-muted">
              How my programming journey began in elementary school with Scratch
              programming, robotics workshops, and the inspiring influence of my
              IT professional grandparents during ages 11-13.
            </p>
          </header>

          <!-- Blog Post Content -->
          <div class="blog-content">
            <!-- Introduction -->
            <section class="mb-5">
              <div class="alert alert-info border-0 mb-4">
                <i class="bi bi-info-circle me-2"></i>
                <strong>Foundation Years:</strong> This post covers my earliest
                programming experiences from 5th-7th grade (ages 11-13), when I
                first discovered the world of coding through visual programming
                and hands-on robotics.
              </div>

              <p class="fs-5">
                Every programmer has an origin story, and mine begins in the
                colorful, block-based world of
                <strong>Scratch programming</strong> during my elementary school
                years. Looking back, those early experiences between ages 11-13
                laid the foundation for everything that would follow in my
                programming journey.
              </p>
            </section>

            <!-- Scratch Programming Discovery -->
            <section class="mb-5">
              <h2 class="h3 text-primary mb-4">
                <i class="bi bi-puzzle me-2"></i>Discovering Scratch: My Gateway
                to Programming
              </h2>

              <div class="row">
                <div class="col-md-8">
                  <p>
                    My programming journey began in elementary school during
                    5th-7th grade when I was first introduced to
                    <strong>Scratch</strong>. This visual programming language
                    opened up a whole new world for me, allowing me to create
                    interactive stories, games, and animations through
                    drag-and-drop coding blocks.
                  </p>

                  <p>
                    What made Scratch so appealing was its visual nature.
                    Instead of intimidating lines of text-based code, I could
                    see the logic flow through colorful blocks that snapped
                    together like puzzle pieces. This approach made programming
                    concepts accessible and fun, turning abstract ideas like
                    loops, conditionals, and variables into tangible, visual
                    elements I could manipulate.
                  </p>

                  <div class="bg-light p-4 rounded mb-4">
                    <h4 class="h5 text-primary mb-3">
                      My First Scratch Projects
                    </h4>
                    <ul class="mb-0">
                      <li>
                        <strong>Interactive Stories:</strong> Created branching
                        narratives where users could make choices
                      </li>
                      <li>
                        <strong>Simple Games:</strong> Built basic arcade-style
                        games with scoring systems
                      </li>
                      <li>
                        <strong>Animations:</strong> Designed animated sequences
                        with multiple sprites and backgrounds
                      </li>
                      <li>
                        <strong>Educational Tools:</strong> Developed simple
                        quiz programs and learning aids
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                      <i class="bi bi-puzzle-fill display-4 mb-3"></i>
                      <h5>Scratch Programming</h5>
                      <p class="small mb-0">
                        Visual programming language<br />Perfect for
                        beginners<br />Ages 11-13
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            <!-- Robotics Workshops -->
            <section class="mb-5">
              <h2 class="h3 text-primary mb-4">
                <i class="bi bi-robot me-2"></i>Hands-On Learning: Robotics
                Workshops
              </h2>

              <p>
                During this period, I developed a strong interest in
                <strong>robotics</strong> and actively attended special robotics
                classes and workshops. These hands-on experiences taught me not
                just programming concepts, but also problem-solving skills and
                logical thinking that would prove invaluable throughout my
                educational journey.
              </p>

              <div class="row">
                <div class="col-md-6">
                  <div class="card border-success mb-3">
                    <div class="card-header bg-success text-white">
                      <h5 class="mb-0">
                        <i class="bi bi-gear-fill me-2"></i>Technical Skills
                      </h5>
                    </div>
                    <div class="card-body">
                      <ul class="mb-0">
                        <li>Basic robot construction and assembly</li>
                        <li>Sensor integration and calibration</li>
                        <li>Simple programming for robot movement</li>
                        <li>Problem-solving through trial and error</li>
                      </ul>
                    </div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="card border-info mb-3">
                    <div class="card-header bg-info text-white">
                      <h5 class="mb-0">
                        <i class="bi bi-lightbulb-fill me-2"></i>Soft Skills
                      </h5>
                    </div>
                    <div class="card-body">
                      <ul class="mb-0">
                        <li>Logical thinking and systematic approach</li>
                        <li>Patience and persistence in debugging</li>
                        <li>Creative problem-solving techniques</li>
                        <li>Understanding cause-and-effect relationships</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <p>
                The robotics workshops were particularly valuable because they
                bridged the gap between abstract programming concepts and
                real-world applications. When I programmed a robot to navigate a
                maze or respond to sensor inputs, I could immediately see the
                results of my code in action. This immediate feedback loop was
                incredibly motivating and helped solidify my understanding of
                programming fundamentals.
              </p>
            </section>

            <!-- Family Influence -->
            <section class="mb-5">
              <h2 class="h3 text-primary mb-4">
                <i class="bi bi-people-fill me-2"></i>Family Influence: IT
                Professional Grandparents
              </h2>

              <div class="bg-primary text-white p-4 rounded mb-4">
                <h4 class="mb-3">
                  <i class="bi bi-heart-fill me-2"></i>The Power of Family
                  Inspiration
                </h4>
                <p class="mb-0">
                  A crucial factor in my early interest was my family
                  background. Both of my grandparents were
                  <strong>IT professionals and software designers</strong> who
                  regularly shared fascinating stories about problem-solving in
                  technology. Their experiences and passion for the field
                  sparked my own curiosity and showed me the real-world
                  applications of programming and technology.
                </p>
              </div>

              <p>
                Having grandparents who were deeply involved in the IT industry
                provided me with a unique perspective on technology from an
                early age. They didn't just see computers as tools or toys—they
                understood them as powerful instruments for solving complex
                problems and creating innovative solutions.
              </p>

              <div class="row">
                <div class="col-md-6">
                  <h5 class="text-primary mb-3">Stories That Inspired</h5>
                  <ul>
                    <li>
                      Real-world problem-solving scenarios from their
                      professional experience
                    </li>
                    <li>
                      Evolution of technology from early computers to modern
                      systems
                    </li>
                    <li>
                      The importance of logical thinking in software design
                    </li>
                    <li>
                      Career possibilities and opportunities in the IT field
                    </li>
                  </ul>
                </div>
                <div class="col-md-6">
                  <h5 class="text-primary mb-3">Values They Instilled</h5>
                  <ul>
                    <li>
                      Persistence in the face of challenging technical problems
                    </li>
                    <li>Attention to detail and systematic approaches</li>
                    <li>
                      Continuous learning and adaptation to new technologies
                    </li>
                    <li>
                      Understanding technology as a tool for positive impact
                    </li>
                  </ul>
                </div>
              </div>

              <p>
                Their influence went beyond just technical knowledge. They
                helped me understand that programming wasn't just about writing
                code—it was about thinking systematically, breaking down complex
                problems into manageable pieces, and finding creative solutions.
                These fundamental problem-solving skills became the cornerstone
                of my approach to learning and development.
              </p>
            </section>

            <!-- Key Takeaways -->
            <section class="mb-5">
              <h2 class="h3 text-primary mb-4">
                <i class="bi bi-key-fill me-2"></i>Foundation Skills Developed
              </h2>

              <div class="row">
                <div class="col-md-4">
                  <div class="card h-100 border-primary">
                    <div class="card-body text-center">
                      <i class="bi bi-brain text-primary display-4 mb-3"></i>
                      <h5 class="card-title">Logical Thinking</h5>
                      <p class="card-text">
                        Developed systematic approaches to problem-solving
                        through visual programming and robotics challenges.
                      </p>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="card h-100 border-success">
                    <div class="card-body text-center">
                      <i
                        class="bi bi-lightbulb text-success display-4 mb-3"
                      ></i>
                      <h5 class="card-title">Creative Problem-Solving</h5>
                      <p class="card-text">
                        Learned to approach challenges from multiple angles and
                        find innovative solutions through hands-on
                        experimentation.
                      </p>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="card h-100 border-info">
                    <div class="card-body text-center">
                      <i
                        class="bi bi-arrow-repeat text-info display-4 mb-3"
                      ></i>
                      <h5 class="card-title">Iterative Learning</h5>
                      <p class="card-text">
                        Understood the importance of testing, debugging, and
                        continuously improving solutions through trial and
                        error.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            <!-- Conclusion -->
            <section class="mb-5">
              <div class="bg-light p-4 rounded">
                <h3 class="text-primary mb-3">
                  <i class="bi bi-arrow-right-circle me-2"></i>Setting the Stage
                  for Future Learning
                </h3>
                <p class="mb-3">
                  These early experiences with Scratch programming and robotics,
                  combined with the inspiring influence of my IT professional
                  grandparents, created a solid foundation for my programming
                  journey. The visual nature of Scratch made programming
                  concepts accessible, while robotics workshops provided
                  hands-on experience with real-world applications.
                </p>
                <p class="mb-3">
                  More importantly, this period taught me that programming is
                  fundamentally about problem-solving and creative thinking. The
                  technical skills I learned were valuable, but the mindset and
                  approach to learning that I developed during these formative
                  years would prove even more crucial as I progressed to more
                  advanced programming languages and concepts.
                </p>
                <p class="mb-0">
                  As I moved forward to high school, I carried with me not just
                  knowledge of basic programming concepts, but also a deep
                  appreciation for the power of technology to solve problems and
                  create meaningful solutions. This foundation would serve me
                  well as I embarked on more formal computer science education.
                </p>
              </div>
            </section>

            <!-- Navigation -->
            <section class="mb-4">
              <div class="d-flex justify-content-between align-items-center">
                <a href="../pages/blog.html" class="btn btn-outline-primary">
                  <i class="bi bi-arrow-left me-2"></i>Back to Blog
                </a>
                <div class="text-center">
                  <small class="text-muted d-block">Part 1 of 5</small>
                  <small class="text-muted">2011-2013 • Ages 11-13</small>
                </div>
                <a
                  href="blog-post-high-school-education.html"
                  class="btn btn-primary"
                >
                  Next: High School Education
                  <i class="bi bi-arrow-right ms-2"></i>
                </a>
              </div>
            </section>
          </div>
        </div>
      </article>
    </main>

    <!-- Footer -->
    <footer class="bg-dark text-white py-4 mt-5">
      <div class="container">
        <div class="row">
          <div class="col-md-6">
            <p>&copy; 2024 My ICT Portfolio. All rights reserved.</p>
          </div>
          <div class="col-md-6 text-md-end">
            <p>HZ University of Applied Sciences - ICT Program</p>
          </div>
        </div>
      </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  </body>
</html>
